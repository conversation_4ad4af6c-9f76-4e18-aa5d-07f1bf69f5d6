import { useRef, useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>dal,
    Breadcrumb,
    Row,
    Col,
    Tag,
    Select,
    Descriptions,
    Typography,
    Collapse,
    Spin
} from "antd";
import { EyeOutlined, SearchOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import i18n from "../../../../i18n/index.ts";
import { AuditChangesDiff } from "../../../../components/audit";
import {
    getAudits,
    getAuditDetails,
    getModelTypes,
    getEventTypes,
    clearError
} from "../../../../features/admin/auditSlice.ts";
import { Audit } from "../../../../types/audit.ts";
import "./AuditModal.css";

const { Text } = Typography;
const { Panel } = Collapse;

function ManageAudits() {
    const { t } = useTranslation();
    const currentLang = i18n.language;
    const actionRef: any = useRef<any>();
    const dispatch: any = useDispatch();
    const [loading, setLoading] = useState(false);

    const {
        currentAudit,
        modelTypes,
        eventTypes,
        error
    } = useSelector((state: any) => state.audit);

    console.log(currentAudit);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(15);
    const [total, setTotal] = useState(1);
    const [detailModalVisible, setDetailModalVisible] = useState(false);

    useEffect(() => {
        dispatch(getModelTypes());
        dispatch(getEventTypes());
    }, [dispatch]);

    useEffect(() => {
        if (error) {
            toast.error(error);
            dispatch(clearError());
        }
    }, [error, dispatch]);


    const handleGetAudits = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getAudits({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    const handleViewDetails = async (record: Audit) => {
        try {
            setLoading(true);
            setDetailModalVisible(true);
            await dispatch(getAuditDetails(record.id)).unwrap();
            setLoading(false);
        } catch (error) {
            setLoading(false);
            toast.error(t("messages.error"));
        }
    };

    const getEventColor = (event: string) => {
        switch (event) {
            case 'created': return 'success';
            case 'updated': return 'warning';
            case 'deleted': return 'error';
            case 'restored': return 'processing';
            default: return 'default';
        }
    };

    const formatChanges = (changes: Record<string, any>) => {
        if (!changes || Object.keys(changes).length === 0) {
            return <Text type="secondary">{t("audits.no_changes")}</Text>;
        }

        return (
            <Collapse size="small" ghost>
                <Panel header={`${Object.keys(changes).length} ${t("audits.changes")}`} key="1">
                    <Descriptions size="small" column={1}>
                        {Object.entries(changes).map(([field, value]) => (
                            <Descriptions.Item key={field} label={field}>
                                <Text code>{JSON.stringify(value)}</Text>
                            </Descriptions.Item>
                        ))}
                    </Descriptions>
                </Panel>
            </Collapse>
        );
    };

    const columns: any = [
        {
            title: t("audits.labels.event"),
            dataIndex: "event",
            render: (_: any, record: Audit) => (
                <Tag color={getEventColor(record.event)}>{record.event_name}</Tag>
            ),
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.event_type")}
                    options={eventTypes}
                />
            ),
        },
        {
            title: t("audits.labels.user"),
            dataIndex: "user_name",
            render: (text: string, record: Audit) => (
                <div>
                    <div>{text}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                        {record.user?.type?.replace('App\\Models\\', '')}
                    </Text>
                </div>
            ),
        },
        {
            title: t("audits.labels.model"),
            dataIndex: "model_type",
            render: (_: any, record: Audit) => record.model_name,
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.model_type")}
                    options={modelTypes}
                />
            ),
        },
        {
            title: t("audits.labels.date"),
            dataIndex: "created_at",
            valueType: "dateRange",
            render: (_: any, record: Audit) => record.human_date,
            sorter: true,
        },
        {
            title: t("audits.labels.ip_address"),
            dataIndex: "ip_address",
        },
        {
            title: t("audits.labels.changes"),
            width: 500,
            dataIndex: "changes",
            search: false,
            render: (changes: Record<string, any>) => formatChanges(changes),
        },
        {
            title: t("audits.labels.actions"),
            fixed: "right",
            width: 120,
            search: false,
            render: (_: any, record: Audit) => (
                <Button
                    className="btn-view"
                    onClick={() => handleViewDetails(record)}
                    size="small"
                >{t("common.showDetails", "Afficher details")}</Button>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.system")}</Link>,
        },
        {
            title: t("audits.title"),
        },
    ];

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("audits.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey="id"
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetAudits(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button
                                key="refresh"
                                icon={<SearchOutlined />}
                                onClick={() => actionRef.current?.reload()}
                            >
                                {t("common.refresh")}
                            </Button>
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1100}
                title={null}
                open={detailModalVisible}
                onCancel={() => setDetailModalVisible(false)}
                footer={null}
                destroyOnClose
                className="modern-audit-modal"
                centered
            >
                {(currentAudit?.data && loading) ? (
                    <div className="flex justify-center items-center py-20">
                        <Spin size="large" />
                    </div>
                ) : currentAudit?.data ? (
                    <div className="modern-audit-content">
                        {/* Header avec événement */}
                        <div className="audit-header">
                            <div className="header-content">
                                <div className="event-badge-container">
                                    <div className={`event-badge event-${currentAudit.data.event}`}>
                                        <div className="event-icon">
                                            {currentAudit.data.event === 'created' ? '✓' :
                                             currentAudit.data.event === 'updated' ? '✎' :
                                             currentAudit.data.event === 'deleted' ? '✗' : '●'}
                                        </div>
                                        <span className="event-text">{currentAudit.data.event_name}</span>
                                    </div>
                                </div>
                                <div className="audit-meta">
                                    <h2 className="audit-title">{t("audits.details")}</h2>
                                    <p className="audit-subtitle">
                                        {currentAudit.data.model_name} • {currentAudit.data.human_date}
                                    </p>
                                </div>
                                <button
                                    className="close-btn"
                                    onClick={() => setDetailModalVisible(false)}
                                >
                                    ✕
                                </button>
                            </div>
                        </div>

                        {/* Contenu principal */}
                        <div className="audit-body">
                            <div className="audit-grid">
                                {/* Carte Utilisateur */}
                                <div className="audit-card user-card">
                                    <div className="card-header">
                                        <div className="card-icon user-icon">👤</div>
                                        <h3 className="card-title">{t("audits.user_info")}</h3>
                                    </div>
                                    <div className="card-content">
                                        <div className="info-item">
                                            <span className="info-label">{t("audits.labels.user")}</span>
                                            <span className="info-value">{currentAudit.data.user_name}</span>
                                        </div>
                                        <div className="info-item">
                                            <span className="info-label">Email</span>
                                            <span className="info-value">{currentAudit.data.user?.email}</span>
                                        </div>
                                        <div className="info-item">
                                            <span className="info-label">Type</span>
                                            <span className="info-value">
                                                {currentAudit.data.user?.type?.toUpperCase() ||
                                                 currentAudit.data.user_type?.replace('App\\Models\\', '')}
                                            </span>
                                        </div>
                                        {currentAudit.data.user?.roles && (
                                            <div className="info-item">
                                                <span className="info-label">Rôles</span>
                                                <div className="roles-container">
                                                    {currentAudit.data.user.roles.map((role: string, index: number) => (
                                                        <span key={index} className="role-badge">{role}</span>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Carte Objet */}
                                <div className="audit-card object-card">
                                    <div className="card-header">
                                        <div className="card-icon object-icon">📄</div>
                                        <h3 className="card-title">{t("audits.labels.model")}</h3>
                                    </div>
                                    <div className="card-content">
                                        <div className="info-item">
                                            <span className="info-label">Nom</span>
                                            <span className="info-value">{currentAudit.data.model_name}</span>
                                        </div>
                                        <div className="info-item">
                                            <span className="info-label">ID</span>
                                            <span className="info-value">#{currentAudit.data.auditable_id}</span>
                                        </div>
                                        <div className="info-item">
                                            <span className="info-label">Type</span>
                                            <span className="info-value small">{currentAudit.data.auditable_type}</span>
                                        </div>
                                        {currentAudit.data.auditable?.name && (
                                            <div className="info-item">
                                                <span className="info-label">Nom de l'objet</span>
                                                <span className="info-value">{currentAudit.data.auditable.name}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Carte Contexte */}
                                <div className="audit-card context-card">
                                    <div className="card-header">
                                        <div className="card-icon context-icon">🌐</div>
                                        <h3 className="card-title">Contexte</h3>
                                    </div>
                                    <div className="card-content">
                                        <div className="info-item">
                                            <span className="info-label">{t("audits.labels.date")}</span>
                                            <span className="info-value">{currentAudit.data.formatted_date}</span>
                                        </div>
                                        <div className="info-item">
                                            <span className="info-label">{t("audits.labels.ip_address")}</span>
                                            <span className="info-value mono">{currentAudit.data.ip_address}</span>
                                        </div>
                                        <div className="info-item">
                                            <span className="info-label">Audit ID</span>
                                            <span className="info-value">#{currentAudit.data.id}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Section Modifications */}
                            <div className="changes-section">
                                <div className="audit-card changes-card full-width">
                                    <div className="card-header">
                                        <div className="card-icon changes-icon">🔄</div>
                                        <h3 className="card-title">{t("audits.changes_detail")}</h3>
                                    </div>
                                    <div className="card-content">
                                        {currentAudit.data.changes && Object.keys(currentAudit.data.changes).length > 0 ? (
                                            <AuditChangesDiff
                                                oldValues={currentAudit.data.old_values}
                                                newValues={currentAudit.data.new_values}
                                                changes={currentAudit.data.changes}
                                            />
                                        ) : (
                                            <div className="no-changes">
                                                <div className="no-changes-icon">📝</div>
                                                <p className="no-changes-text">{t("audits.no_changes")}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Section Technique */}
                            <div className="technical-section">
                                <div className="audit-card technical-card full-width">
                                    <div className="card-header">
                                        <div className="card-icon technical-icon">⚙️</div>
                                        <h3 className="card-title">{t("audits.technical_details")}</h3>
                                    </div>
                                    <div className="card-content">
                                        <div className="technical-grid">
                                            <div className="tech-item">
                                                <span className="tech-label">URL</span>
                                                <code className="tech-value">{currentAudit.data.url}</code>
                                            </div>
                                            <div className="tech-item">
                                                <span className="tech-label">User Agent</span>
                                                <code className="tech-value small">{currentAudit.data.user_agent}</code>
                                            </div>
                                            {currentAudit.data.tags && (
                                                <div className="tech-item">
                                                    <span className="tech-label">Tags</span>
                                                    <span className="tech-value">{currentAudit.data.tags}</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : null}
            </Modal>
        </>
    );
}

export default ManageAudits;
