import { useRef, useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>dal,
    Breadcrumb,
    Row,
    Col,
    Tag,
    Select,
    Descriptions,
    Typography,
    Collapse,
    Spin
} from "antd";
import { EyeOutlined, SearchOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import i18n from "../../../../i18n/index.ts";
import { AuditChangesDiff } from "../../../../components/audit";
import {
    getAudits,
    getAuditDetails,
    getModelTypes,
    getEventTypes,
    clearError
} from "../../../../features/admin/auditSlice.ts";
import { Audit } from "../../../../types/audit.ts";
import "./AuditModal.css";

const { Text } = Typography;
const { Panel } = Collapse;

function ManageAudits() {
    const { t } = useTranslation();
    const currentLang = i18n.language;
    const actionRef: any = useRef<any>();
    const dispatch: any = useDispatch();
    const [loading, setLoading] = useState(false);

    const {
        currentAudit,
        modelTypes,
        eventTypes,
        error
    } = useSelector((state: any) => state.audit);

    console.log(currentAudit);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(15);
    const [total, setTotal] = useState(1);
    const [detailModalVisible, setDetailModalVisible] = useState(false);

    useEffect(() => {
        dispatch(getModelTypes());
        dispatch(getEventTypes());
    }, [dispatch]);

    useEffect(() => {
        if (error) {
            toast.error(error);
            dispatch(clearError());
        }
    }, [error, dispatch]);


    const handleGetAudits = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getAudits({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    const handleViewDetails = async (record: Audit) => {
        try {
            setLoading(true);
            setDetailModalVisible(true);
            await dispatch(getAuditDetails(record.id)).unwrap();
            setLoading(false);
        } catch (error) {
            setLoading(false);
            toast.error(t("messages.error"));
        }
    };

    const getEventColor = (event: string) => {
        switch (event) {
            case 'created': return 'success';
            case 'updated': return 'warning';
            case 'deleted': return 'error';
            case 'restored': return 'processing';
            default: return 'default';
        }
    };

    const formatChanges = (changes: Record<string, any>) => {
        if (!changes || Object.keys(changes).length === 0) {
            return <Text type="secondary">{t("audits.no_changes")}</Text>;
        }

        return (
            <Collapse size="small" ghost>
                <Panel header={`${Object.keys(changes).length} ${t("audits.changes")}`} key="1">
                    <Descriptions size="small" column={1}>
                        {Object.entries(changes).map(([field, value]) => (
                            <Descriptions.Item key={field} label={field}>
                                <Text code>{JSON.stringify(value)}</Text>
                            </Descriptions.Item>
                        ))}
                    </Descriptions>
                </Panel>
            </Collapse>
        );
    };

    const columns: any = [
        {
            title: t("audits.labels.event"),
            dataIndex: "event",
            render: (_: any, record: Audit) => (
                <Tag color={getEventColor(record.event)}>{record.event_name}</Tag>
            ),
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.event_type")}
                    options={eventTypes}
                />
            ),
        },
        {
            title: t("audits.labels.user"),
            dataIndex: "user_name",
            render: (text: string, record: Audit) => (
                <div>
                    <div>{text}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                        {record.user?.type?.replace('App\\Models\\', '')}
                    </Text>
                </div>
            ),
        },
        {
            title: t("audits.labels.model"),
            dataIndex: "model_type",
            render: (_: any, record: Audit) => record.model_name,
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.model_type")}
                    options={modelTypes}
                />
            ),
        },
        {
            title: t("audits.labels.date"),
            dataIndex: "created_at",
            valueType: "dateRange",
            render: (_: any, record: Audit) => record.human_date,
            sorter: true,
        },
        {
            title: t("audits.labels.ip_address"),
            dataIndex: "ip_address",
        },
        {
            title: t("audits.labels.changes"),
            width: 500,
            dataIndex: "changes",
            search: false,
            render: (changes: Record<string, any>) => formatChanges(changes),
        },
        {
            title: t("audits.labels.actions"),
            fixed: "right",
            width: 120,
            search: false,
            render: (_: any, record: Audit) => (
                <Button
                    className="btn-view"
                    onClick={() => handleViewDetails(record)}
                    size="small"
                >{t("common.showDetails", "Afficher details")}</Button>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.system")}</Link>,
        },
        {
            title: t("audits.title"),
        },
    ];

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("audits.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey="id"
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetAudits(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button
                                key="refresh"
                                icon={<SearchOutlined />}
                                onClick={() => actionRef.current?.reload()}
                            >
                                {t("common.refresh")}
                            </Button>
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    <div className="flex items-center justify-between">
                        <span className="text-lg font-semibold">{t("audits.details")}</span>
                        {currentAudit?.data && (
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                currentAudit.data.event === 'created' ? 'bg-green-100 text-green-800' :
                                currentAudit.data.event === 'updated' ? 'bg-yellow-100 text-yellow-800' :
                                currentAudit.data.event === 'deleted' ? 'bg-red-100 text-red-800' :
                                'bg-blue-100 text-blue-800'
                            }`}>
                                {currentAudit.data.event_name}
                            </span>
                        )}
                    </div>
                }
                open={detailModalVisible}
                onCancel={() => setDetailModalVisible(false)}
                footer={
                    <div className="text-right">
                        <Button
                            onClick={() => setDetailModalVisible(false)}
                            type="primary"
                        >
                            {t("common.close")}
                        </Button>
                    </div>
                }
                destroyOnClose
                className="audit-detail-modal"
            >
                {(currentAudit?.data && loading) ? (
                    <div className="flex justify-center items-center py-16">
                        <Spin size="large" />
                    </div>
                ) : currentAudit?.data ? (
                    <div className="space-y-6">
                        {/* Informations Générales */}
                        <div className="border border-gray-200 rounded-lg p-5">
                            <h3 className="text-lg font-semibold mb-4 text-gray-900 border-b border-gray-100 pb-2">
                                {t("audits.basic_info")}
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500 mb-1">
                                            {t("audits.labels.date")}
                                        </dt>
                                        <dd className="text-sm text-gray-900">
                                            {currentAudit.data.formatted_date}
                                            <span className="block text-xs text-gray-500 mt-1">
                                                {currentAudit.data.human_date}
                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500 mb-1">
                                            {t("audits.labels.model")}
                                        </dt>
                                        <dd className="text-sm text-gray-900">
                                            {currentAudit.data.model_name}
                                            <span className="block text-xs text-gray-500 mt-1">
                                                ID: {currentAudit.data.auditable_id}
                                            </span>
                                        </dd>
                                    </div>
                                </div>
                                <div className="space-y-4">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500 mb-1">
                                            {t("audits.labels.user")}
                                        </dt>
                                        <dd className="text-sm text-gray-900">
                                            {currentAudit.data.user_name}
                                            <span className="block text-xs text-gray-500 mt-1">
                                                {currentAudit.data.user?.email}
                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500 mb-1">
                                            {t("audits.labels.ip_address")}
                                        </dt>
                                        <dd className="text-sm text-gray-900 font-mono">
                                            {currentAudit.data.ip_address}
                                        </dd>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Modifications */}
                        <div className="border border-gray-200 rounded-lg p-5">
                            <h3 className="text-lg font-semibold mb-4 text-gray-900 border-b border-gray-100 pb-2">
                                {t("audits.changes_detail")}
                            </h3>
                            {currentAudit.data.changes && Object.keys(currentAudit.data.changes).length > 0 ? (
                                <AuditChangesDiff
                                    oldValues={currentAudit.data.old_values}
                                    newValues={currentAudit.data.new_values}
                                    changes={currentAudit.data.changes}
                                />
                            ) : (
                                <div className="text-center py-8">
                                    <div className="text-gray-400 text-sm">
                                        {t("audits.no_changes")}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Détails Techniques */}
                        <div className="border border-gray-200 rounded-lg p-5">
                            <h3 className="text-lg font-semibold mb-4 text-gray-900 border-b border-gray-100 pb-2">
                                {t("audits.technical_details")}
                            </h3>
                            <div className="space-y-4">
                                <div>
                                    <dt className="text-sm font-medium text-gray-500 mb-1">
                                        {t("audits.labels.url")}
                                    </dt>
                                    <dd className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all">
                                        {currentAudit.data.url}
                                    </dd>
                                </div>
                                <div>
                                    <dt className="text-sm font-medium text-gray-500 mb-1">
                                        {t("audits.labels.user_agent")}
                                    </dt>
                                    <dd className="text-xs text-gray-700 font-mono bg-gray-50 p-2 rounded break-all">
                                        {currentAudit.data.user_agent}
                                    </dd>
                                </div>
                                {currentAudit.data.tags && (
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500 mb-1">
                                            {t("audits.labels.tags")}
                                        </dt>
                                        <dd className="text-sm text-gray-900">
                                            {currentAudit.data.tags}
                                        </dd>
                                    </div>
                                )}
                                <div className="pt-2 border-t border-gray-100">
                                    <span className="text-xs text-gray-400">
                                        Audit ID: {currentAudit.data.id}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : null}
            </Modal>
        </>
    );
}

export default ManageAudits;
