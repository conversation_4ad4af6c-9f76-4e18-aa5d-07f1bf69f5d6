import { useRef, useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    Modal,
    Breadcrumb,
    Row,
    Col,
    Tag,
    Select,
    DatePicker,
    Input,
    Space,
    Descriptions,
    Typography,
    Collapse
} from "antd";
import { EyeOutlined, SearchOutlined, FilterOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { toast, ToastContainer } from "react-toastify";
import dayjs from "dayjs";
import i18n from "../../../../i18n/index.ts";
import { AuditChangesDiff } from "../../../../components/audit";
import {
    getAudits,
    getAuditDetails,
    getModelTypes,
    getEventTypes,
    clearError
} from "../../../../features/admin/auditSlice.ts";
import { Audit, AuditFilters } from "../../../../types/audit.ts";

const { RangePicker } = DatePicker;
const { Text } = Typography;
const { Panel } = Collapse;

function ManageAudits() {
    const { t } = useTranslation();
    const currentLang = i18n.language;
    const actionRef: any = useRef<any>();
    const dispatch: any = useDispatch();

    const {
        paginatedAudits,
        currentAudit,
        modelTypes,
        eventTypes,
        loading,
        error
    } = useSelector((state: any) => state.audit);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(15);
    const [total, setTotal] = useState(1);
    const [detailModalVisible, setDetailModalVisible] = useState(false);
    const [filters, setFilters] = useState<AuditFilters>({});

    useEffect(() => {
        dispatch(getModelTypes());
        dispatch(getEventTypes());
    }, [dispatch]);

    useEffect(() => {
        if (error) {
            toast.error(error);
            dispatch(clearError());
        }
    }, [error, dispatch]);

    // Fetch audits with filters
    const handleGetAudits = async (params: any, sort: any, filter: any) => {
        const auditFilters: AuditFilters = {
            ...filters,
            perPage: pageSize,
            page: pageNumber,
            ...params
        };

        try {
            const result = await dispatch(getAudits(auditFilters)).unwrap();
            setTotal(result.meta.total);
            return result.data;
        } catch (error) {
            console.error("Error fetching audits:", error);
            return [];
        }
    };

    // Handle view audit details
    const handleViewDetails = async (record: Audit) => {
        try {
            await dispatch(getAuditDetails(record.id)).unwrap();
            setDetailModalVisible(true);
        } catch (error) {
            toast.error(t("messages.error"));
        }
    };

    // Get event badge color
    const getEventColor = (event: string) => {
        switch (event) {
            case 'created': return 'success';
            case 'updated': return 'warning';
            case 'deleted': return 'error';
            case 'restored': return 'processing';
            default: return 'default';
        }
    };

    // Format changes for display
    const formatChanges = (changes: Record<string, any>) => {
        if (!changes || Object.keys(changes).length === 0) {
            return <Text type="secondary">{t("audits.no_changes")}</Text>;
        }

        return (
            <Collapse size="small" ghost>
                <Panel header={`${Object.keys(changes).length} ${t("audits.changes")}`} key="1">
                    <Descriptions size="small" column={1}>
                        {Object.entries(changes).map(([field, value]) => (
                            <Descriptions.Item key={field} label={field}>
                                <Text code>{JSON.stringify(value)}</Text>
                            </Descriptions.Item>
                        ))}
                    </Descriptions>
                </Panel>
            </Collapse>
        );
    };

    const columns: any = [
        {
            title: t("audits.labels.event"),
            dataIndex: "event_name",
            width: 120,
            search: false,
            render: (text: string, record: Audit) => (
                <Tag color={getEventColor(record.event)}>{text}</Tag>
            ),
        },
        {
            title: t("audits.labels.user"),
            dataIndex: "user_name",
            width: 150,
            search: false,
            render: (text: string, record: Audit) => (
                <div>
                    <div>{text}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                        {record.user?.type?.replace('App\\Models\\', '')}
                    </Text>
                </div>
            ),
        },
        {
            title: t("audits.labels.model"),
            dataIndex: "model_name",
            width: 150,
            search: false,
        },
        {
            title: t("audits.labels.date"),
            dataIndex: "human_date",
            width: 180,
            search: false,
            sorter: true,
        },
        {
            title: t("audits.labels.ip_address"),
            dataIndex: "ip_address",
            width: 120,
            search: false,
        },
        {
            title: t("audits.labels.changes"),
            dataIndex: "changes",
            search: false,
            render: (changes: Record<string, any>) => formatChanges(changes),
        },
        {
            title: t("audits.labels.actions"),
            fixed: "right",
            width: 80,
            search: false,
            render: (_: any, record: Audit) => (
                <Button
                    className="btn-view"
                    icon={<EyeOutlined />}
                    onClick={() => handleViewDetails(record)}
                    size="small"
                />
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.system")}</Link>,
        },
        {
            title: t("audits.title"),
        },
    ];

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <ToastContainer className="z-50" />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("audits.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey="id"
                        request={handleGetAudits}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{ x: 1200 }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Space key="filters" wrap>
                                <Select
                                    placeholder={t("audits.filters.event_type")}
                                    style={{ width: 150 }}
                                    allowClear
                                    value={filters.event}
                                    onChange={(value) => {
                                        setFilters({ ...filters, event: value });
                                        actionRef.current?.reload();
                                    }}
                                    options={eventTypes}
                                />
                                <Select
                                    placeholder={t("audits.filters.model_type")}
                                    style={{ width: 200 }}
                                    allowClear
                                    value={filters.model_type}
                                    onChange={(value) => {
                                        setFilters({ ...filters, model_type: value });
                                        actionRef.current?.reload();
                                    }}
                                    options={modelTypes}
                                />
                                <RangePicker
                                    placeholder={[t("audits.filters.start_date"), t("audits.filters.end_date")]}
                                    value={filters.start_date && filters.end_date ? [
                                        dayjs(filters.start_date),
                                        dayjs(filters.end_date)
                                    ] : null}
                                    onChange={(dates) => {
                                        if (dates) {
                                            setFilters({
                                                ...filters,
                                                start_date: dates[0]?.format('YYYY-MM-DD'),
                                                end_date: dates[1]?.format('YYYY-MM-DD')
                                            });
                                        } else {
                                            setFilters({
                                                ...filters,
                                                start_date: undefined,
                                                end_date: undefined
                                            });
                                        }
                                        actionRef.current?.reload();
                                    }}
                                />
                                <Input
                                    placeholder={t("audits.filters.search")}
                                    style={{ width: 200 }}
                                    value={filters.search}
                                    onChange={(e) => {
                                        setFilters({ ...filters, search: e.target.value });
                                    }}
                                    onPressEnter={() => actionRef.current?.reload()}
                                    suffix={<SearchOutlined />}
                                />
                                <Button
                                    icon={<FilterOutlined />}
                                    onClick={() => {
                                        setFilters({});
                                        actionRef.current?.reload();
                                    }}
                                >
                                    {t("common.clear_filters")}
                                </Button>
                            </Space>
                        ]}
                    />
                </Col>
            </Row>

            {/* Audit Detail Modal */}
            <Modal
                width={1000}
                title={t("audits.details")}
                open={detailModalVisible}
                onCancel={() => setDetailModalVisible(false)}
                footer={null}
                destroyOnClose
            >
                {currentAudit && (
                    <div className="space-y-4">
                        <Descriptions title={t("audits.basic_info")} bordered column={2}>
                            <Descriptions.Item label={t("audits.labels.event")}>
                                <Tag color={getEventColor(currentAudit.event)}>
                                    {currentAudit.event_name}
                                </Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.user")}>
                                {currentAudit.user_name}
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.model")}>
                                {currentAudit.model_name}
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.date")}>
                                {currentAudit.formatted_date}
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.ip_address")}>
                                {currentAudit.ip_address}
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.url")}>
                                <Text code>{currentAudit.url}</Text>
                            </Descriptions.Item>
                        </Descriptions>

                        {currentAudit.changes && Object.keys(currentAudit.changes).length > 0 && (
                            <AuditChangesDiff
                                oldValues={currentAudit.old_values}
                                newValues={currentAudit.new_values}
                                changes={currentAudit.changes}
                            />
                        )}

                        <Descriptions title={t("audits.technical_details")} bordered column={1} size="small">
                            <Descriptions.Item label={t("audits.labels.user_agent")}>
                                <Text code style={{ fontSize: '12px' }}>
                                    {currentAudit.user_agent}
                                </Text>
                            </Descriptions.Item>
                        </Descriptions>
                    </div>
                )}
            </Modal>
        </>
    );
}

export default ManageAudits;
