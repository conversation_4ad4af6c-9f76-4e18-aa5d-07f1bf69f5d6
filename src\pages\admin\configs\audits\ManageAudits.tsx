import { useRef, useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Breadcrumb,
    Row,
    Col,
    Tag,
    Select,
    Descriptions,
    Typography,
    Collapse,
    Spin
} from "antd";
import { EyeOutlined, SearchOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import i18n from "../../../../i18n/index.ts";
import { AuditChangesDiff } from "../../../../components/audit";
import {
    getAudits,
    getAuditDetails,
    getModelTypes,
    getEventTypes,
    clearError
} from "../../../../features/admin/auditSlice.ts";
import { Audit } from "../../../../types/audit.ts";

const { Text } = Typography;
const { Panel } = Collapse;

function ManageAudits() {
    const { t } = useTranslation();
    const currentLang = i18n.language;
    const actionRef: any = useRef<any>();
    const dispatch: any = useDispatch();
    const [loading, setLoading] = useState(false);

    const {
        currentAudit,
        modelTypes,
        eventTypes,
        error
    } = useSelector((state: any) => state.audit);

    console.log(currentAudit);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(15);
    const [total, setTotal] = useState(1);
    const [detailModalVisible, setDetailModalVisible] = useState(false);

    useEffect(() => {
        dispatch(getModelTypes());
        dispatch(getEventTypes());
    }, [dispatch]);

    useEffect(() => {
        if (error) {
            toast.error(error);
            dispatch(clearError());
        }
    }, [error, dispatch]);


    const handleGetAudits = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getAudits({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    const handleViewDetails = async (record: Audit) => {
        try {
            setLoading(true);
            setDetailModalVisible(true);
            await dispatch(getAuditDetails(record.id)).unwrap();
            setLoading(false);
        } catch (error) {
            setLoading(false);
            toast.error(t("messages.error"));
        }
    };

    const getEventColor = (event: string) => {
        switch (event) {
            case 'created': return 'success';
            case 'updated': return 'warning';
            case 'deleted': return 'error';
            case 'restored': return 'processing';
            default: return 'default';
        }
    };

    const formatChanges = (changes: Record<string, any>) => {
        if (!changes || Object.keys(changes).length === 0) {
            return <Text type="secondary">{t("audits.no_changes")}</Text>;
        }

        return (
            <Collapse size="small" ghost>
                <Panel header={`${Object.keys(changes).length} ${t("audits.changes")}`} key="1">
                    <Descriptions size="small" column={1}>
                        {Object.entries(changes).map(([field, value]) => (
                            <Descriptions.Item key={field} label={field}>
                                <Text code>{JSON.stringify(value)}</Text>
                            </Descriptions.Item>
                        ))}
                    </Descriptions>
                </Panel>
            </Collapse>
        );
    };

    const columns: any = [
        {
            title: t("audits.labels.event"),
            dataIndex: "event",
            render: (_: any, record: Audit) => (
                <Tag color={getEventColor(record.event)}>{record.event_name}</Tag>
            ),
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.event_type")}
                    options={eventTypes}
                />
            ),
        },
        {
            title: t("audits.labels.user"),
            dataIndex: "user_name",
            render: (text: string, record: Audit) => (
                <div>
                    <div>{text}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                        {record.user?.type?.replace('App\\Models\\', '')}
                    </Text>
                </div>
            ),
        },
        {
            title: t("audits.labels.model"),
            dataIndex: "model_type",
            render: (_: any, record: Audit) => record.model_name,
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.model_type")}
                    options={modelTypes}
                />
            ),
        },
        {
            title: t("audits.labels.date"),
            dataIndex: "created_at",
            valueType: "dateRange",
            render: (_: any, record: Audit) => record.human_date,
            sorter: true,
        },
        {
            title: t("audits.labels.ip_address"),
            dataIndex: "ip_address",
        },
        {
            title: t("audits.labels.changes"),
            width: 500,
            dataIndex: "changes",
            search: false,
            render: (changes: Record<string, any>) => formatChanges(changes),
        },
        {
            title: t("audits.labels.actions"),
            fixed: "right",
            width: 120,
            search: false,
            render: (_: any, record: Audit) => (
                <Button
                    className="btn-view"
                    onClick={() => handleViewDetails(record)}
                    size="small"
                >{t("common.showDetails", "Afficher details")}</Button>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.system")}</Link>,
        },
        {
            title: t("audits.title"),
        },
    ];

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("audits.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey="id"
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetAudits(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button
                                key="refresh"
                                icon={<SearchOutlined />}
                                onClick={() => actionRef.current?.reload()}
                            >
                                {t("common.refresh")}
                            </Button>
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1000}
                title={
                    <div className="flex items-center gap-3">
                        <span className="text-lg font-medium">{t("audits.details")}</span>
                        {currentAudit?.data && (
                            <Tag
                                color={getEventColor(currentAudit.data.event)}
                                className="px-2 py-1 text-sm font-medium"
                            >
                                {currentAudit.data.event_name}
                            </Tag>
                        )}
                    </div>
                }
                open={detailModalVisible}
                onCancel={() => setDetailModalVisible(false)}
                footer={
                    <div className="flex justify-end">
                        <Button
                            key="close"
                            onClick={() => setDetailModalVisible(false)}
                            className="px-6"
                        >
                            {t("common.close")}
                        </Button>
                    </div>
                }
                destroyOnClose
                className="audit-detail-modal"
            >
                {(currentAudit?.data && loading) ? (
                    <div className="flex justify-center items-center py-16">
                        <Spin size="large" />
                    </div>
                ) : currentAudit?.data ? (
                    <div className="space-y-8">
                        {/* Basic Information */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h3 className="text-base font-semibold mb-4 text-gray-800">
                                {t("audits.basic_info")}
                            </h3>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.event")}
                                    </label>
                                    <div className="mt-1">
                                        <Tag color={getEventColor(currentAudit.data.event)} className="text-sm">
                                            {currentAudit.data.event_name}
                                        </Tag>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.date")}
                                    </label>
                                    <div className="mt-1">
                                        <div className="text-sm font-medium">{currentAudit.data.formatted_date}</div>
                                        <div className="text-xs text-gray-500">{currentAudit.data.human_date}</div>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.model")}
                                    </label>
                                    <div className="mt-1">
                                        <div className="text-sm font-medium">{currentAudit.data.model_name}</div>
                                        <div className="text-xs text-gray-500">{currentAudit.data.auditable_type}</div>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.model_id")}
                                    </label>
                                    <div className="mt-1">
                                        <Tag color="blue" className="text-sm">ID: {currentAudit.data.auditable_id}</Tag>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* User Information */}
                        <div className="bg-blue-50 p-4 rounded-lg">
                            <h3 className="text-base font-semibold mb-4 text-gray-800">
                                {t("audits.user_info")}
                            </h3>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.user")}
                                    </label>
                                    <div className="mt-1">
                                        <div className="text-sm font-medium">{currentAudit.data.user_name}</div>
                                        <div className="text-xs text-gray-500">{currentAudit.data.user?.email}</div>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.user_type")}
                                    </label>
                                    <div className="mt-1">
                                        <Tag color="green" className="text-sm">
                                            {currentAudit.data.user?.type?.toUpperCase() || currentAudit.data.user_type?.replace('App\\Models\\', '')}
                                        </Tag>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.user_id")}
                                    </label>
                                    <div className="mt-1">
                                        <Tag color="blue" className="text-sm">ID: {currentAudit.data.user_id}</Tag>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">
                                        {t("audits.labels.user_roles")}
                                    </label>
                                    <div className="mt-1 flex gap-1 flex-wrap">
                                        {currentAudit.data.user?.roles?.map((role: string, index: number) => (
                                            <Tag key={index} color="purple" className="text-xs">
                                                {role}
                                            </Tag>
                                        )) || <span className="text-sm text-gray-500">-</span>}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Auditable Object */}
                        {currentAudit.data.auditable && (
                            <div className="bg-green-50 p-4 rounded-lg">
                                <h3 className="text-base font-semibold mb-4 text-gray-800">
                                    {t("audits.auditable_object")}
                                </h3>
                                <div className="grid grid-cols-3 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">
                                            {t("audits.labels.object_name")}
                                        </label>
                                        <div className="mt-1 text-sm font-medium">
                                            {currentAudit.data.auditable.name || currentAudit.data.auditable.type}
                                        </div>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">
                                            {t("audits.labels.object_type")}
                                        </label>
                                        <div className="mt-1">
                                            <Tag color="cyan" className="text-sm">{currentAudit.data.auditable.type}</Tag>
                                        </div>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">
                                            {t("audits.labels.object_id")}
                                        </label>
                                        <div className="mt-1">
                                            <Tag color="blue" className="text-sm">ID: {currentAudit.data.auditable.id}</Tag>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Changes Section */}
                        <div className="bg-yellow-50 p-4 rounded-lg">
                            <h3 className="text-base font-semibold mb-4 text-gray-800">
                                {t("audits.changes_detail")}
                            </h3>
                            {currentAudit.data.changes && Object.keys(currentAudit.data.changes).length > 0 ? (
                                <AuditChangesDiff
                                    oldValues={currentAudit.data.old_values}
                                    newValues={currentAudit.data.new_values}
                                    changes={currentAudit.data.changes}
                                />
                            ) : (
                                <div className="text-center py-6">
                                    <Text type="secondary" className="text-sm italic">
                                        {t("audits.no_changes")}
                                    </Text>
                                </div>
                            )}
                        </div>

                        {/* Technical Details */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h3 className="text-base font-semibold mb-4 text-gray-800">
                                {t("audits.technical_details")}
                            </h3>
                            <div className="space-y-3">
                                <div className="flex items-center gap-3">
                                    <label className="text-sm font-medium text-gray-600 w-24">
                                        {t("audits.labels.ip_address")}:
                                    </label>
                                    <Tag color="orange" className="text-sm">{currentAudit.data.ip_address}</Tag>
                                </div>
                                <div className="flex items-start gap-3">
                                    <label className="text-sm font-medium text-gray-600 w-24 mt-1">
                                        {t("audits.labels.url")}:
                                    </label>
                                    <code className="text-xs bg-gray-100 px-2 py-1 rounded break-all flex-1">
                                        {currentAudit.data.url}
                                    </code>
                                </div>
                                <div className="flex items-start gap-3">
                                    <label className="text-sm font-medium text-gray-600 w-24 mt-1">
                                        {t("audits.labels.user_agent")}:
                                    </label>
                                    <code className="text-xs bg-gray-100 px-2 py-1 rounded break-all flex-1">
                                        {currentAudit.data.user_agent}
                                    </code>
                                </div>
                                {currentAudit.data.tags && (
                                    <div className="flex items-center gap-3">
                                        <label className="text-sm font-medium text-gray-600 w-24">
                                            {t("audits.labels.tags")}:
                                        </label>
                                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                                            {currentAudit.data.tags}
                                        </code>
                                    </div>
                                )}
                                <div className="flex items-center gap-3">
                                    <label className="text-sm font-medium text-gray-600 w-24">
                                        {t("audits.labels.audit_id")}:
                                    </label>
                                    <Tag color="purple" className="text-sm">Audit ID: {currentAudit.data.id}</Tag>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : null}
            </Modal>
        </>
    );
}

export default ManageAudits;
