import { useRef, useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Breadcrumb,
    Row,
    Col,
    Tag,
    Select,
    Descriptions,
    Typography,
    Collapse,
    Spin
} from "antd";
import { EyeOutlined, SearchOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import i18n from "../../../../i18n/index.ts";
import { AuditChangesDiff } from "../../../../components/audit";
import {
    getAudits,
    getAuditDetails,
    getModelTypes,
    getEventTypes,
    clearError
} from "../../../../features/admin/auditSlice.ts";
import { Audit } from "../../../../types/audit.ts";

const { Text } = Typography;
const { Panel } = Collapse;

function ManageAudits() {
    const { t } = useTranslation();
    const currentLang = i18n.language;
    const actionRef: any = useRef<any>();
    const dispatch: any = useDispatch();
    const [loading, setLoading] = useState(false);

    const {
        currentAudit,
        modelTypes,
        eventTypes,
        error
    } = useSelector((state: any) => state.audit);

    console.log(currentAudit);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(15);
    const [total, setTotal] = useState(1);
    const [detailModalVisible, setDetailModalVisible] = useState(false);

    useEffect(() => {
        dispatch(getModelTypes());
        dispatch(getEventTypes());
    }, [dispatch]);

    useEffect(() => {
        if (error) {
            toast.error(error);
            dispatch(clearError());
        }
    }, [error, dispatch]);


    const handleGetAudits = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getAudits({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    const handleViewDetails = async (record: Audit) => {
        try {
            setLoading(true);
            setDetailModalVisible(true);
            await dispatch(getAuditDetails(record.id)).unwrap();
            setLoading(false);
        } catch (error) {
            setLoading(false);
            toast.error(t("messages.error"));
        }
    };

    const getEventColor = (event: string) => {
        switch (event) {
            case 'created': return 'success';
            case 'updated': return 'warning';
            case 'deleted': return 'error';
            case 'restored': return 'processing';
            default: return 'default';
        }
    };

    const formatChanges = (changes: Record<string, any>) => {
        if (!changes || Object.keys(changes).length === 0) {
            return <Text type="secondary">{t("audits.no_changes")}</Text>;
        }

        return (
            <Collapse size="small" ghost>
                <Panel header={`${Object.keys(changes).length} ${t("audits.changes")}`} key="1">
                    <Descriptions size="small" column={1}>
                        {Object.entries(changes).map(([field, value]) => (
                            <Descriptions.Item key={field} label={field}>
                                <Text code>{JSON.stringify(value)}</Text>
                            </Descriptions.Item>
                        ))}
                    </Descriptions>
                </Panel>
            </Collapse>
        );
    };

    const columns: any = [
        {
            title: t("audits.labels.event"),
            dataIndex: "event",
            render: (_: any, record: Audit) => (
                <Tag color={getEventColor(record.event)}>{record.event_name}</Tag>
            ),
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.event_type")}
                    options={eventTypes}
                />
            ),
        },
        {
            title: t("audits.labels.user"),
            dataIndex: "user_name",
            render: (text: string, record: Audit) => (
                <div>
                    <div>{text}</div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                        {record.user?.type?.replace('App\\Models\\', '')}
                    </Text>
                </div>
            ),
        },
        {
            title: t("audits.labels.model"),
            dataIndex: "model_type",
            render: (_: any, record: Audit) => record.model_name,
            renderFormItem: () => (
                <Select
                    showSearch
                    allowClear
                    placeholder={t("audits.filters.model_type")}
                    options={modelTypes}
                />
            ),
        },
        {
            title: t("audits.labels.date"),
            dataIndex: "created_at",
            valueType: "dateRange",
            render: (_: any, record: Audit) => record.human_date,
            sorter: true,
        },
        {
            title: t("audits.labels.ip_address"),
            dataIndex: "ip_address",
        },
        {
            title: t("audits.labels.changes"),
            width: 500,
            dataIndex: "changes",
            search: false,
            render: (changes: Record<string, any>) => formatChanges(changes),
        },
        {
            title: t("audits.labels.actions"),
            fixed: "right",
            width: 120,
            search: false,
            render: (_: any, record: Audit) => (
                <Button
                    className="btn-view"
                    onClick={() => handleViewDetails(record)}
                    size="small"
                >{t("common.showDetails", "Afficher details")}</Button>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.system")}</Link>,
        },
        {
            title: t("audits.title"),
        },
    ];

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("audits.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey="id"
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetAudits(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button
                                key="refresh"
                                icon={<SearchOutlined />}
                                onClick={() => actionRef.current?.reload()}
                            >
                                {t("common.refresh")}
                            </Button>
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={1200}
                title={
                    <div className="flex items-center gap-2">
                        <span>{t("audits.details")}</span>
                    </div>
                }
                open={detailModalVisible}
                onCancel={() => setDetailModalVisible(false)}
                footer={[
                    <Button key="close" onClick={() => setDetailModalVisible(false)}>
                        {t("common.close")}
                    </Button>
                ]}
                destroyOnClose
            >
                {(currentAudit?.data && loading )? (
                    <div className="flex justify-center items-center py-10">
                        <Spin />
                    </div>
                ) : (
                    <div className="space-y-6">
                        <Descriptions title={t("audits.basic_info")} bordered column={2} size="small">
                            <Descriptions.Item label={t("audits.labels.event")}>
                                <Tag color={getEventColor(currentAudit?.data.event)}>
                                    {currentAudit?.data.event_name}
                                </Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.date")}>
                                <div>
                                    <div>{currentAudit?.data.formatted_date}</div>
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                        {currentAudit?.data.human_date}
                                    </Text>
                                </div>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.model")}>
                                <div>
                                    <div><Text strong>{currentAudit?.data.model_name}</Text></div>
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                        {currentAudit?.data.auditable_type}
                                    </Text>
                                </div>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.model_id")}>
                                <Tag color="blue">ID: {currentAudit?.data.auditable_id}</Tag>
                            </Descriptions.Item>
                        </Descriptions>

                        <Descriptions title={t("audits.user_info")} bordered column={2} size="small">
                            <Descriptions.Item label={t("audits.labels.user")}>
                                <div>
                                    <div><Text strong>{currentAudit?.data.user_name}</Text></div>
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                        {currentAudit?.data.user?.email}
                                    </Text>
                                </div>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.user_type")}>
                                <Tag color="green">
                                    {currentAudit?.data.user?.type?.toUpperCase() || currentAudit?.data.user_type?.replace('App\\Models\\', '')}
                                </Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.user_id")}>
                                <Tag color="blue">ID: {currentAudit?.data.user_id}</Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.user_roles")}>
                                <div className="flex gap-1 flex-wrap">
                                    {currentAudit?.data.user?.roles?.map((role: string, index: number) => (
                                        <Tag key={index} color="purple" style={{ fontSize: '11px' }}>
                                            {role}
                                        </Tag>
                                    )) || <Text type="secondary">-</Text>}
                                </div>
                            </Descriptions.Item>
                        </Descriptions>

                        {currentAudit?.data.auditable && (
                            <Descriptions title={t("audits.auditable_object")} bordered column={2} size="small">
                                <Descriptions.Item label={t("audits.labels.object_name")}>
                                    <Text strong>{currentAudit?.data.auditable.name || currentAudit.data.auditable.type}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label={t("audits.labels.object_type")}>
                                    <Tag color="cyan">{currentAudit?.data.auditable.type}</Tag>
                                </Descriptions.Item>
                                <Descriptions.Item label={t("audits.labels.object_id")}>
                                    <Tag color="blue">ID: {currentAudit?.data.auditable.id}</Tag>
                                </Descriptions.Item>
                            </Descriptions>
                        )}

                        {currentAudit?.data.changes && Object.keys(currentAudit?.data.changes).length > 0 ? (
                            <div>
                                <h4 className="text-lg font-semibold mb-3">{t("audits.changes_detail")}</h4>
                                <AuditChangesDiff
                                    oldValues={currentAudit?.data.old_values}
                                    newValues={currentAudit?.data.new_values}
                                    changes={currentAudit?.data.changes}
                                />
                            </div>
                        ) : (
                            <div className="text-center py-4">
                                <Text type="secondary" italic>
                                    {t("audits.no_changes")}
                                </Text>
                            </div>
                        )}

                        <Descriptions title={t("audits.technical_details")} bordered column={1} size="small">
                            <Descriptions.Item label={t("audits.labels.ip_address")}>
                                <Tag color="orange">{currentAudit?.data.ip_address}</Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.url")}>
                                <Text code style={{ fontSize: '12px', wordBreak: 'break-all' }}>
                                    {currentAudit?.data.url}
                                </Text>
                            </Descriptions.Item>
                            <Descriptions.Item label={t("audits.labels.user_agent")}>
                                <Text code style={{ fontSize: '11px', wordBreak: 'break-all' }}>
                                    {currentAudit?.data.user_agent}
                                </Text>
                            </Descriptions.Item>
                            {currentAudit?.data.tags && (
                                <Descriptions.Item label={t("audits.labels.tags")}>
                                    <Text code>{currentAudit?.data.tags}</Text>
                                </Descriptions.Item>
                            )}
                            <Descriptions.Item label={t("audits.labels.audit_id")}>
                                <Tag color="purple">Audit ID: {currentAudit?.data.id}</Tag>
                            </Descriptions.Item>
                        </Descriptions>
                    </div>
                )}
            </Modal>
        </>
    );
}

export default ManageAudits;
