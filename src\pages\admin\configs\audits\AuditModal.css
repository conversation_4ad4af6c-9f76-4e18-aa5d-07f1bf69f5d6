/* Modern Audit Modal - Style Moderne avec Cartes */
.modern-audit-modal .ant-modal-content {
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(62, 62, 62, 0.15);
  overflow: hidden;
  border: none;
  padding: 0;
}

.modern-audit-modal .ant-modal-body {
  padding: 0;
  background: transparent;
  max-height: 85vh;
  overflow-y: auto;
}

.modern-audit-modal .ant-modal-close {
  display: none;
}

/* Header Section */
.audit-header {
  background: url('./assets/images/bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #bc0202;
  padding: 30px;
  color: white;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.event-badge-container {
  flex-shrink: 0;
}

.event-badge {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 14px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.event-created {
  background: rgba(95, 134, 112, 0.3);
  color: #ffffff;
}

.event-updated {
  background: rgba(234, 134, 24, 0.3);
  color: #ffffff;
}

.event-deleted {
  background: rgba(188, 2, 2, 0.3);
  color: #ffffff;
}

.event-icon {
  font-size: 16px;
  font-weight: bold;
}

.audit-meta {
  flex: 1;
}

.audit-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  color: white;
}

.audit-subtitle {
  font-size: 16px;
  margin: 5px 0 0 0;
  color: rgba(255, 255, 255, 0.8);
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Body Section */
.audit-body {
  background: #f8fafc;
  padding: 30px;
  min-height: 400px;
}

.audit-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

/* Cards */
.audit-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.audit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(95, 134, 112, 0.2);
  background: linear-gradient(135deg, rgba(95, 134, 112, 0.05) 0%, rgba(62, 62, 62, 0.05) 100%);
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
}

.user-icon {
  background: #BC0202;
  color: white;
}

.object-icon {
  background: #5F8670;
  color: white;
}

.context-icon {
  background: #ea8618;
  color: white;
}

.changes-icon {
  background: #3e3e3e;
  color: white;
}

.technical-icon {
  background: #5F8670;
  color: white;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #3e3e3e;
  margin: 0;
}

.card-content {
  padding: 24px;
}

/* Info Items */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.info-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  font-size: 13px;
  font-weight: 600;
  color: #5F8670;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #3e3e3e;
  text-align: right;
  flex: 1;
  margin-left: 16px;
}

.info-value.small {
  font-size: 12px;
  color: #5F8670;
}

.info-value.mono {
  font-family: 'SF Mono', 'Monaco', monospace;
  font-size: 13px;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 6px;
}

/* Roles */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: flex-end;
}

.role-badge {
  background: #BC0202;
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Full Width Cards */
.full-width {
  grid-column: 1 / -1;
}

.changes-section,
.technical-section {
  margin-top: 20px;
}

/* Technical Grid */
.technical-grid {
  display: grid;
  gap: 16px;
}

.tech-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tech-label {
  font-size: 12px;
  font-weight: 600;
  color: #5F8670;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tech-value {
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  font-family: 'SF Mono', 'Monaco', monospace;
  font-size: 13px;
  color: #3e3e3e;
  word-break: break-all;
  border: 1px solid #5F8670;
}

.tech-value.small {
  font-size: 11px;
  line-height: 1.4;
}

/* No Changes State */
.no-changes {
  text-align: center;
  padding: 40px 20px;
  color: #5F8670;
}

.no-changes-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-changes-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .audit-grid {
    grid-template-columns: 1fr;
  }

  .audit-header {
    padding: 20px;
  }

  .audit-title {
    font-size: 24px;
  }

  .audit-body {
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .close-btn {
    position: static;
    align-self: flex-end;
  }
}

/* Scrollbar */
.modern-audit-modal .ant-modal-body::-webkit-scrollbar {
  width: 6px;
}

.modern-audit-modal .ant-modal-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.modern-audit-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.modern-audit-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
