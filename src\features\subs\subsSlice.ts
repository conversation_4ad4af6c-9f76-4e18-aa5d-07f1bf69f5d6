import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../config/axiosConfig.tsx";

const URL = "/subscriptions";

export const getSubscriptionsAll: any = createAsyncThunk(
  "getSubscriptionsAll",
  async (_: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}-all`;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const getSubscriptions: any = createAsyncThunk(
  "getSubscriptions",
  async (
    data: {
      pageNumber: number;
      perPage: number;
      params: any;
      sort: any;
      filter: any;
    },
    thunkAPI: any
  ) => {
    try {
      const { id_client, id_subs_type, is_social_affair, id_trip, status, id_periodicity, is_reversed } = data.params;

      const sort = data.sort;
      let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;

      // Handle search parameters
      const searchParams = [];
      if (id_client) {
        searchParams.push(`id_client:${id_client}`);
      }
      if (id_subs_type) {
        searchParams.push(`id_subs_type:${id_subs_type}`);
      }
      if (is_social_affair !== undefined && is_social_affair !== null && is_social_affair !== '') {
        searchParams.push(`is_social_affair:${is_social_affair}`);
      }
      if (id_trip) {
        searchParams.push(`id_trip:${id_trip}`);
      }

      // Add periodicity filter
      if (id_periodicity) {
        searchParams.push(`id_periodicity:${id_periodicity}`);
      }

      // Add is_reversed filter (boolean/integer)
      if (is_reversed !== undefined && is_reversed !== null && is_reversed !== '') {
        searchParams.push(`is_reversed:${is_reversed}`);
      }

      // Add status filter (PAYED, NOTPAYED, CANCELED)
      if (status) {
        searchParams.push(`status:${status}`);
      }

      // Add search parameters to URL
      if (searchParams.length > 0) {
        url += `&search=${searchParams.join(";")}`;
      }

      // Handle sorting
      const orderBy = [];
      const sortedBy = [];
      for (const [field, order] of Object.entries(sort)) {
        orderBy.push(field);
        sortedBy.push(order === "ascend" ? "asc" : "desc");
      }
      if (orderBy.length > 0) {
        url += `&orderBy=${orderBy.join(",")}&sortedBy=${sortedBy.join(",")}`;
      }

      // Handle additional filters from data.filter if present
      if (data.filter && Object.keys(data.filter).length > 0) {
        const filterParams = [];
        for (const [key, value] of Object.entries(data.filter)) {
          if (value !== undefined && value !== null && value !== '') {
            filterParams.push(`${key}:${value}`);
          }
        }
        if (filterParams.length > 0) {
          url += `&filter=${filterParams.join(";")}`;
        }
      }
      const joint = "&searchJoin=and";
      url += joint;
      const resp: any = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storeSubscription: any = createAsyncThunk(
  "storeSubscription",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const formData = new FormData();
      const { photo, rest_days, ...otherData } = data;

      Object.keys(otherData).forEach((key) => {
        if (otherData[key] !== undefined && otherData[key] !== null) {
          formData.append(key, String(otherData[key]));
        }
      });

      if (photo) {
        formData.append("photo", photo);
      }

      const resp = await api.post(url, formData);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const updateSubscription: any = createAsyncThunk(
  "updateSubscription",
  async (data: any, thunkAPI: any) => {
    try {
      const { id, photo, rest_days,  ...otherData } = data;
      const url: string = `${URL}/${id}`;
      const formData = new FormData();

       // Handle rest_days as an array
       if (Array.isArray(rest_days)) {
        rest_days.forEach((day: string | number) => {
          formData.append("rest_days[]", String(day));
        });
      }

      Object.keys(otherData).forEach((key) => {
        if (otherData[key] !== undefined && otherData[key] !== null) {
          formData.append(key, String(otherData[key]));
        }
      });

      if (
        photo &&
        !(typeof photo === "string" && photo.startsWith("subscriptions/"))
      ) {
        let fileToUpload = photo;

        if (typeof photo === "string") {
          try {
            const response = await fetch(photo);
            const blob = await response.blob();
            fileToUpload = new File([blob], "subscription_photo.jpg", {
              type: "image/jpeg",
            });
          } catch (error) {
            console.error("Error processing photo:", error);
          }
        }

        if (fileToUpload instanceof File) {
          formData.append("photo", fileToUpload, "subscription_photo.jpg");
        }
      }

      formData.append("_method", "PUT");

      const resp: any = await api.post(url, formData);
      return resp.data;
    } catch (error: any) {
      console.error("Error in updateSubscription:", error);
      if (error.response?.status === 403) {
        window.location.href = "/unauthorized";
      }
      return thunkAPI.rejectWithValue(
        error.response?.data || "An unexpected error occurred."
      );
    }
  }
);

export const deleteSubscription: any = createAsyncThunk(
  "deleteSubscription",
  async (id: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}/${id}`;
      const resp: any = await api.delete(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const verifySocialAffairId = createAsyncThunk(
  "verifySocialAffairId",
  async (id: string, thunkAPI: any) => {
    try {
      const apiResponse = await api.get(`/social-affairs/verify/${id}`);
      if (!apiResponse.data.exists) {
        return thunkAPI.rejectWithValue("ID not found in system");
      }

      return { success: true };
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

export const storeClientWithSubscription: any = createAsyncThunk(
  "storeClientWithSubscription",
  async (data: any, thunkAPI: any) => {
    try {
      const url: string = `${URL}`;
      const formData = new FormData();
      const { photo, rest_days, clientData, ...otherData } = data;


      // Add client data as JSON
      if (clientData) {
        formData.append("clientData", clientData);
      }

        // Handle rest_days as an array
        if (Array.isArray(rest_days)) {
          rest_days.forEach((day: string | number) => {
            formData.append("rest_days[]", String(day));
          });
        }

      // Handle other subscription data
      Object.keys(otherData).forEach((key) => {
        if (otherData[key] !== undefined && otherData[key] !== null) {
          formData.append(key, String(otherData[key]));
        }
      });

      // Handle photo separately if it exists
      if (photo) {
        formData.append("photo", photo);
      }

      const resp = await api.post(url, formData);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data);
      } else {
        return thunkAPI.rejectWithValue("An unexpected error occurred.");
      }
    }
  }
);

