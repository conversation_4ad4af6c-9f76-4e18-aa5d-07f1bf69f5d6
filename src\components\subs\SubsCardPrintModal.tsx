import { Modal, Button, Form, Input, Select } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { assets } from "../../assets/assets.ts";
import { formatImagePath } from "../../tools/helpers.ts";
import moment from "moment/moment";
import { useEffect, useRef, useState } from "react";
import { useReactToPrint } from "react-to-print";
import { storeSubsCard } from "../../features/admin/subsCardSlice.ts";
import { useDispatch } from "react-redux";
import { getMotifDuplicatesAll } from "../../features/admin/motifDuplicateSlice.ts";
import { getSubsCardsBySubscription } from "../../features/admin/subsCardSlice.ts";
import { toast } from "react-toastify";

const SubsCardPrintModal = ({
  isDuplicated,
  isVisible,
  onClose,
  abnRecord,
  onPrintSuccess,
}: any) => {
  const dispatch = useDispatch<any>();
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  const contentRef = useRef<HTMLDivElement>(null);

  const [form] = Form.useForm();

  //*******
  // Function for select options motifs on Sub Card Modal
  // ********************************** */
  const generateSelectOptions = (data: any) => {
    let options: any[] = [];
    data.map((m: any) => {
      options.push({ value: m.id, label: m[`nom_${currentLang}`] });
    });
    return options;
  };

  const [motifs, setMotifs] = useState<any[]>([]);

  const getMotifs = async () => {
    try {
      //setLoading(true);
      const result = await dispatch(getMotifDuplicatesAll()).unwrap();
      setMotifs(generateSelectOptions(result.data));
    } catch (error: any) {
    } finally {
      //setLoading(false);
    }
  };
  useEffect(() => {
    form.resetFields();
    getMotifs();
  }, []);

  const [showAmountInput, setShowAmountInput] = useState(false);
  const changeAmount = (value: string) => {
    form.resetFields(["duplicate_amount"]);
    if (value === "free_edit") {
      setShowAmountInput(true);
    } else {
      setShowAmountInput(false);
    }
  };

  const handlePrint = async () => {
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });

    let payload = {};

    payload = isDuplicated ? {
        duplicate_amount: form.getFieldValue("duplicate_amount"),
        id_motif_duplicate: form.getFieldValue("motif_duplicate_id"),
        type_amount: form.getFieldValue("type_amount"),
        id_subscription: abnRecord.id,
    } : {
        id_subscription: abnRecord.id,
    }
    try {
        await dispatch(storeSubsCard(payload)).unwrap();

        await dispatch(getSubsCardsBySubscription(abnRecord.id)).unwrap();

        toast.update(toastId, {
            render: t("messages.success"),
            type: "success",
            isLoading: false,
            autoClose: 3000
        });
        if (onPrintSuccess && typeof onPrintSuccess === 'function') {
            onPrintSuccess();
        }
        handleReset();
    } catch (error: any) {
        toast.update(toastId, {
            render: t("messages." + error.message) || t("messages.error"),
            type: "error",
            isLoading: false,
            autoClose: 3000
        });
    }
  };


  const confirmSubmit = () => {
        const modal = Modal.confirm({
            title: t("manage_newSubs.confirmAction"),
            content: t("manage_newSubs.confirmPrint"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
              modal.destroy();
              await handlePrint();
            },
            centered: true,
        });
    };


  const handleReset = () => {
    setShowAmountInput(false);
    form.resetFields();
    onClose()
  }

  return (
    <Modal
      width={800}
      title={
        isDuplicated
          ? t("manage_newSubs.modal.cardDuplicate")
          : t("manage_newSubs.modal.cardTitle")
      }
      open={isVisible}
      onCancel={handleReset}
      footer={[
        <Button
          key="print"
          icon={<PrinterOutlined />}
          onClick={() => confirmSubmit()}
          className="!flex items-center gap-2"
        >
          {t("manage_newSubs.modal.print")}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t("manage_newSubs.modal.close")}
        </Button>,
      ]}
    >
      {abnRecord && (
        <div className="flex justify-center items-center p-6">
          <div
            className="relative p-4 w-[550px] bg-white rounded shadow border border-gray-100 overflow-hidden"
            ref={contentRef}
          >
            {/* Bandes jaunes diagonales courbées */}
            <div
              className="absolute -top-10 -left-10 w-[140%] h-32 transform -rotate-12 origin-top-left rounded-tr-[100px]"
              style={{
                backgroundColor: `${abnRecord?.subs_type?.color}33`,
                backgroundImage: `linear-gradient(to bottom right, ${abnRecord?.subs_type?.color}33, #0000)`,
              }}
            ></div>
            <div
              className="absolute -bottom-10 -right-10 w-[140%] h-32 transform rotate-12 origin-bottom-right rounded-tl-[100px]"
              style={{
                backgroundColor: `${abnRecord?.subs_type?.color}33`,
                backgroundImage: `linear-gradient(to top left, ${abnRecord?.subs_type?.color}33, #0000)`,
              }}
            ></div>

            {/* Contenu principal */}
            <div className="relative z-10">
              {/* En-tête avec image */}
              <div className="flex bg-gradient-to-r from-black-50/50 to-white-50/50 backdrop-blur-sm">
                <div className=" w-1/4 flex justify-center items-start">
                  <div className="relative">
                    <img
                      src={
                        abnRecord.photo
                          ? formatImagePath(abnRecord.photo)
                          : assets.user
                      }
                      alt="Abonné"
                      className="w-22 h-22 rounded-full border-[3px] border-white shadow object-cover"
                    />
                  </div>
                </div>

                {/* Détails abonné */}
                <div className="w-3/4 pl-8 space-y-4">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-1.5 tracking-tight">
                      {abnRecord?.client?.firstname}{" "}
                      {abnRecord?.client?.lastname}
                    </h2>
                    <p className="text-sm text-gray-600 font-medium">
                      <span className="text-gray-400">identifiant : </span>
                      {abnRecord?.client.identity_number}
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1.5">
                      <p className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
                        Trajet
                      </p>
                      <p className="text-gray-800 font-medium">
                        {abnRecord?.trip[`nom_${currentLang}`]}
                      </p>
                    </div>

                    <div className="space-y-1.5">
                      <p className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
                        Périodicité
                      </p>
                      <p className="text-gray-800 font-medium">
                        {abnRecord?.periodicity?.[`nom_${currentLang}`]}
                        <span className="text-gray-400 ml-1.5"></span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Section inférieure */}
              <div className="p-8 pt-6">
                <div className="flex justify-between items-center border-t border-dashed border-gray-200 pt-6">
                  <div className="space-y-1">
                    <p className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
                      Date d'expiration
                    </p>
                    <p className="text-gray-800 font-medium text-lg">
                      {moment(abnRecord?.end_date).format("DD MMM YYYY")}
                    </p>
                  </div>

                  <div
                    style={{
                      color: abnRecord?.client?.client_type?.abn_type?.color,
                      borderColor:
                        abnRecord?.client?.client_type?.abn_type?.color,
                    }}
                    className="flex items-center gap-3 px-5 py-2.5 rounded-xl backdrop-blur-sm border"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-semibold tracking-wide">
                      {abnRecord?.subs_type?.[`nom_${currentLang}`]}
                    </span>
                  </div>
                </div>
              </div>

              {/* Pied de page */}
              <div className="bg-gray-50/80 p-4 text-center border-t border-dashed border-gray-200 backdrop-blur-sm">
                <p className="text-xs text-gray-500 tracking-wide">
                  {t("manage_newSubs.card.footerText")}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="flex justify-center items-center p-6">
        {isDuplicated && (
          <Form
            className="form-inputs"
            form={form}
            layout="vertical"
            initialValues={{
              type_amount: "keep_old_amount",
              motif_duplicate_id: motifs[0]?.value,
            }}
          >
            <Form.Item
              label={t("manage_newSubs.modal.motifDuplicate")}
              name="motif_duplicate_id"
              rules={[
                {
                  required: true,
                  message: t("manage_newSubs.errors.motifDuplicate"),
                },
              ]}
            >
              <Select
                defaultValue={motifs[0]?.value}
                placeholder={t("manage_newSubs.modal.motifDuplicate")}
                style={{ width: 450 }}
                options={motifs}
              />
            </Form.Item>

            <Form.Item
              label={t("manage_newSubs.modal.new_amount")}
              name="type_amount"
              rules={[
                {
                  required: true,
                  message: t("manage_newSubs.errors.new_amount"),
                },
              ]}
            >
              <Select
                onChange={changeAmount}
                placeholder={t("manage_newSubs.modal.new_amount")}
                defaultValue="keep_old_amount"
                style={{ width: 450 }}
                options={[
                  {
                    value: "keep_old_amount",
                    label: t("manage_newSubs.select.keep_old_amount"),
                  },
                  { value: "free_edit", label: t("manage_newSubs.select.free_edit") },
                ]}
              />
            </Form.Item>
            {showAmountInput && (
              <Form.Item
                name="duplicate_amount"
                rules={[
                  {
                    required: true,
                    message: t("manage_newSubs.errors.new_amount"),
                  },
                ]}
              >
                <Input
                  type="number"
                  min={0}
                  placeholder={t("manage_newSubs.placeholders.new_amount")}
                />
              </Form.Item>
            )}
          </Form>
        )}
      </div>
    </Modal>
  );
};

export default SubsCardPrintModal;
