{"name": "srtgn-frontend-private", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.2.6", "@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.4", "@ant-design/pro-table": "^3.18.6", "@reduxjs/toolkit": "^2.5.0", "antd": "^5.23.2", "axios": "^1.7.9", "file-saver": "^2.0.5", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^7.2.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.474.0", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-easy-crop": "^5.2.0", "react-i18next": "^15.4.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.0", "react-router-dom": "^7.1.3", "react-to-print": "^3.0.6", "react-toastify": "^11.0.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/file-saver": "^2.0.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^4.5.9"}}