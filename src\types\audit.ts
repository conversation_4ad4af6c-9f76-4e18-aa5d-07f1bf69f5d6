// Audit System TypeScript Interfaces

export interface AuditUser {
  id: number;
  type: string;
  name: string;
  email: string;
  roles: string[];
}

export interface AuditableModel {
  id: number;
  type: string;
  name: string;
}

export interface Audit {
  id: number;
  event: 'created' | 'updated' | 'deleted' | 'restored';
  event_name: string;
  auditable_type: string;
  auditable_id: number;
  model_name: string;
  user_id: number;
  user_type: string;
  user_name: string;
  user: AuditUser;
  auditable: AuditableModel;
  old_values: Record<string, any> | null;
  new_values: Record<string, any> | null;
  changes: Record<string, any>;
  url: string;
  ip_address: string;
  user_agent: string;
  tags: string | null;
  created_at: string;
  updated_at: string;
  formatted_date: string;
  human_date: string;
}

export interface AuditFilters {
  event?: 'created' | 'updated' | 'deleted' | 'restored';
  model_type?: string;
  user_id?: number;
  user_type?: 'App\\Models\\Admin' | 'App\\Models\\Client';
  user_name?: string;
  created_at?: string[] | string;
  start_date?: string;
  end_date?: string;
  ip_address?: string;
  search?: string;
  perPage?: number;
  page?: number;
  pageNumber?: number;
  params?: any;
  sort?: any;
  filter?: any;
}

export interface AuditPaginatedResponse {
  data: Audit[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface AuditStatistics {
  total_audits: number;
  by_event: {
    created: number;
    updated: number;
    deleted: number;
    restored: number;
  };
  by_model: Record<string, number>;
  by_user_type: {
    'App\\Models\\Admin': number;
    'App\\Models\\Client': number;
  };
  recent_activity: Record<string, number>;
}

export interface MostActiveUser {
  user_id: number;
  user_type: string;
  audit_count: number;
  user_name: string;
  email: string;
  type: string;
}

export interface MostAuditedModel {
  auditable_type: string;
  audit_count: number;
  model_name: string;
}

export interface SelectOption {
  value: string;
  label: string;
}

export interface AuditState {
  audits: Audit[];
  paginatedAudits: AuditPaginatedResponse | null;
  statistics: AuditStatistics | null;
  mostActiveUsers: MostActiveUser[];
  mostAuditedModels: MostAuditedModel[];
  recentActivity: Audit[];
  modelTypes: SelectOption[];
  eventTypes: SelectOption[];
  currentAudit: Audit | null;
  loading: boolean;
  error: string | null;
}
