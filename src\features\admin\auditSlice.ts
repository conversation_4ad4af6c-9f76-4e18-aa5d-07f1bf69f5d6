import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";
import { AuditState, AuditFilters } from '../../types/audit.ts';

const URL = '/audits';

const initialState: AuditState = {
    audits: [],
    paginatedAudits: null,
    statistics: null,
    mostActiveUsers: [],
    mostAuditedModels: [],
    recentActivity: [],
    modelTypes: [],
    eventTypes: [],
    currentAudit: null,
    loading: false,
    error: null,
};



export const getAudits = createAsyncThunk(
    "getAudits",
    async (data: any, thunkAPI) => {
        try {
            const { nom_fr, nom_en, nom_ar } = data.params;
            const sort = data.sort;

            let url = `${URL}?page=${data.pageNumber}&perPage=${data.perPage}`;
            const searchParams = [];

            if (nom_fr) searchParams.push(`nom_fr:${nom_fr}`);
            if (nom_en) searchParams.push(`nom_en:${nom_en}`);
            if (nom_ar) searchParams.push(`nom_ar:${nom_ar}`);

            if (searchParams.length > 0) {
                url += `&search=${searchParams.join(';')}`;
            }

            const orderBy = [];
            const sortedBy = [];

            for (const [field, order] of Object.entries(sort)) {
                orderBy.push(field);
                sortedBy.push(order === 'ascend' ? 'asc' : 'desc');
            }

            if (orderBy.length > 0) {
                url += `&orderBy=${orderBy.join(',')}&sortedBy=${sortedBy.join(',')}`;
            }
            const joint = "&searchJoin=and";
            url += joint;
            const resp = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            }
            return thunkAPI.rejectWithValue("An unexpected error occurred.");
        }
    }
);


// Get audit details by ID
export const getAuditDetails: any = createAsyncThunk(
    "getAuditDetails",
    async (id: number, thunkAPI: any) => {
        try {
            const url = `${URL}/${id}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get model audits
export const getModelAudits: any = createAsyncThunk(
    "getModelAudits",
    async ({ modelType, modelId, perPage = 15 }: { modelType: string; modelId: number; perPage?: number }, thunkAPI: any) => {
        try {
            const url = `${URL}/model/${modelType}/${modelId}?perPage=${perPage}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get user audits
export const getUserAudits: any = createAsyncThunk(
    "getUserAudits",
    async ({ userId, userType, perPage = 15 }: { userId: number; userType?: string; perPage?: number }, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            if (userType) params.append('user_type', userType);
            params.append('perPage', perPage.toString());

            const url = `${URL}/user/${userId}?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get my audits (current user)
export const getMyAudits: any = createAsyncThunk(
    "getMyAudits",
    async (perPage: number = 15, thunkAPI: any) => {
        try {
            const url = `${URL}/my-audits?perPage=${perPage}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get event audits
export const getEventAudits: any = createAsyncThunk(
    "getEventAudits",
    async ({ event, perPage = 15 }: { event: string; perPage?: number }, thunkAPI: any) => {
        try {
            const url = `${URL}/event/${event}?perPage=${perPage}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get audit statistics
export const getAuditStatistics: any = createAsyncThunk(
    "getAuditStatistics",
    async ({ startDate, endDate }: { startDate?: string; endDate?: string } = {}, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            const url = `${URL}/statistics?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get most active users
export const getMostActiveUsers: any = createAsyncThunk(
    "getMostActiveUsers",
    async ({ limit = 10, startDate, endDate }: { limit?: number; startDate?: string; endDate?: string } = {}, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            params.append('limit', limit.toString());
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            const url = `${URL}/most-active-users?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get most audited models
export const getMostAuditedModels: any = createAsyncThunk(
    "getMostAuditedModels",
    async ({ limit = 10, startDate, endDate }: { limit?: number; startDate?: string; endDate?: string } = {}, thunkAPI: any) => {
        try {
            const params = new URLSearchParams();
            params.append('limit', limit.toString());
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            const url = `${URL}/most-audited-models?${params.toString()}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get recent activity
export const getRecentActivity: any = createAsyncThunk(
    "getRecentActivity",
    async (limit: number = 20, thunkAPI: any) => {
        try {
            const url = `${URL}/recent-activity?limit=${limit}`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get model types for filtering
export const getModelTypes: any = createAsyncThunk(
    "getModelTypes",
    async (_: any, thunkAPI: any) => {
        try {
            const url = `${URL}/model-types`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

// Get event types for filtering
export const getEventTypes: any = createAsyncThunk(
    "getEventTypes",
    async (_: any, thunkAPI: any) => {
        try {
            const url = `${URL}/event-types`;
            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const auditSlice = createSlice({
    name: 'audit',
    initialState,
    reducers: {
        setCurrentAudit: (state, action) => {
            state.currentAudit = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        },
        clearAudits: (state) => {
            state.audits = [];
            state.paginatedAudits = null;
        },
        clearStatistics: (state) => {
            state.statistics = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // Get audits
            .addCase(getAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedAudits = action.payload;
                state.audits = action.payload.data;
            })
            .addCase(getAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get audit details
            .addCase(getAuditDetails.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAuditDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.currentAudit = action.payload;
            })
            .addCase(getAuditDetails.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get model audits
            .addCase(getModelAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getModelAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedAudits = action.payload;
                state.audits = action.payload.data;
            })
            .addCase(getModelAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get user audits
            .addCase(getUserAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getUserAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedAudits = action.payload;
                state.audits = action.payload.data;
            })
            .addCase(getUserAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get my audits
            .addCase(getMyAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMyAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedAudits = action.payload;
                state.audits = action.payload.data;
            })
            .addCase(getMyAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get event audits
            .addCase(getEventAudits.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getEventAudits.fulfilled, (state, action) => {
                state.loading = false;
                state.paginatedAudits = action.payload;
                state.audits = action.payload.data;
            })
            .addCase(getEventAudits.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get audit statistics
            .addCase(getAuditStatistics.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAuditStatistics.fulfilled, (state, action) => {
                state.loading = false;
                state.statistics = action.payload.data;
            })
            .addCase(getAuditStatistics.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get most active users
            .addCase(getMostActiveUsers.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMostActiveUsers.fulfilled, (state, action) => {
                state.loading = false;
                state.mostActiveUsers = action.payload.data;
            })
            .addCase(getMostActiveUsers.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get most audited models
            .addCase(getMostAuditedModels.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMostAuditedModels.fulfilled, (state, action) => {
                state.loading = false;
                state.mostAuditedModels = action.payload.data;
            })
            .addCase(getMostAuditedModels.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get recent activity
            .addCase(getRecentActivity.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getRecentActivity.fulfilled, (state, action) => {
                state.loading = false;
                state.recentActivity = action.payload.data;
            })
            .addCase(getRecentActivity.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get model types
            .addCase(getModelTypes.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getModelTypes.fulfilled, (state, action) => {
                state.loading = false;
                state.modelTypes = action.payload.data;
            })
            .addCase(getModelTypes.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            })

            // Get event types
            .addCase(getEventTypes.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getEventTypes.fulfilled, (state, action) => {
                state.loading = false;
                state.eventTypes = action.payload.data;
            })
            .addCase(getEventTypes.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload || "An error occurred";
            });
    }
});

export const { setCurrentAudit, clearError, clearAudits, clearStatistics } = auditSlice.actions;
export default auditSlice.reducer;