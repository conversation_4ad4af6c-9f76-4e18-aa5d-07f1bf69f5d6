import React from 'react';
import { Descriptions, Typography, Tag, Space, Divider } from 'antd';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

interface AuditChangesDiffProps {
    oldValues: Record<string, any> | null;
    newValues: Record<string, any> | null;
    changes: Record<string, any>;
}

const AuditChangesDiff: React.FC<AuditChangesDiffProps> = ({
    oldValues,
    newValues,
    changes
}) => {
    const { t } = useTranslation();

    const formatValue = (value: any): string => {
        if (value === null || value === undefined) {
            return t('audits.null_value');
        }
        if (typeof value === 'boolean') {
            return value ? t('common.yes') : t('common.no');
        }
        if (typeof value === 'object') {
            return JSON.stringify(value, null, 2);
        }
        return String(value);
    };

    const getValueColor = (oldVal: any, newVal: any) => {
        if (oldVal === null || oldVal === undefined) {
            return 'success'; // New value added
        }
        if (newVal === null || newVal === undefined) {
            return 'error'; // Value removed
        }
        return 'warning'; // Value changed
    };

    const renderValueComparison = (field: string, changeData: any) => {
        // Handle the API response structure where changes contain {old, new} objects
        if (changeData && typeof changeData === 'object' && 'old' in changeData && 'new' in changeData) {
            const oldVal = changeData.old;
            const newVal = changeData.new;

            return (
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Tag color="warning">{t('audits.value_changed')}</Tag>
                    <div>
                        <Text strong>{t('audits.old_value')}:</Text>
                        <br />
                        <Text
                            code
                            delete
                            style={{ backgroundColor: '#fff2f0', padding: '4px 8px', borderRadius: '4px' }}
                        >
                            {formatValue(oldVal)}
                        </Text>
                    </div>
                    <Divider style={{ margin: '8px 0' }} />
                    <div>
                        <Text strong>{t('audits.new_value')}:</Text>
                        <br />
                        <Text
                            code
                            style={{ backgroundColor: '#f6ffed', padding: '4px 8px', borderRadius: '4px' }}
                        >
                            {formatValue(newVal)}
                        </Text>
                    </div>
                </Space>
            );
        }

        // Handle direct value changes (for created events)
        const oldVal = oldValues?.[field];
        const newVal = newValues?.[field] || changeData;
        const hasOldValue = oldVal !== null && oldVal !== undefined;
        const hasNewValue = newVal !== null && newVal !== undefined;

        if (!hasOldValue && hasNewValue) {
            // Value was added
            return (
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Tag color="success">{t('audits.value_added')}</Tag>
                    <Text code style={{ backgroundColor: '#f6ffed', padding: '4px 8px', borderRadius: '4px' }}>
                        {formatValue(newVal)}
                    </Text>
                </Space>
            );
        }

        if (hasOldValue && !hasNewValue) {
            // Value was removed
            return (
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Tag color="error">{t('audits.value_removed')}</Tag>
                    <Text
                        code
                        delete
                        style={{ backgroundColor: '#fff2f0', padding: '4px 8px', borderRadius: '4px' }}
                    >
                        {formatValue(oldVal)}
                    </Text>
                </Space>
            );
        }

        return (
            <Text code>{formatValue(newVal || oldVal || changeData)}</Text>
        );
    };

    if (!changes || Object.keys(changes).length === 0) {
        return (
            <Text type="secondary" italic>
                {t('audits.no_changes')}
            </Text>
        );
    }

    return (
        <div className="audit-changes-diff">
            <Descriptions
                title={t('audits.field_changes')}
                bordered
                column={1}
                size="small"
                labelStyle={{ width: '200px', fontWeight: 'bold' }}
            >
                {Object.keys(changes).map((field) => {
                    const changeData = changes[field];
                    const oldVal = oldValues?.[field];
                    const newVal = newValues?.[field];

                    return (
                        <Descriptions.Item
                            key={field}
                            label={
                                <Space>
                                    <Text strong>{field}</Text>
                                    <Tag color={getValueColor(oldVal, newVal)} style={{ fontSize: '11px' }}>
                                        {oldVal === null || oldVal === undefined
                                            ? t('audits.added')
                                            : newVal === null || newVal === undefined
                                            ? t('audits.removed')
                                            : t('audits.modified')
                                        }
                                    </Tag>
                                </Space>
                            }
                        >
                            {renderValueComparison(field, changeData)}
                        </Descriptions.Item>
                    );
                })}
            </Descriptions>
        </div>
    );
};

export default AuditChangesDiff;
